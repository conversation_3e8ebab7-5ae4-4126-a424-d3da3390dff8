<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
<!-- Chart.js CSS -->
<style>
    .chart-container {
        position: relative;
        height: 400px;
        margin-bottom: 2rem;
    }
    .info-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }
    .info-box:hover {
        transform: translateY(-5px);
    }
    .info-box h3 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    .info-box p {
        margin: 0;
        opacity: 0.9;
    }
    .deviation-high {
        color: #dc3545;
        font-weight: bold;
    }
    .deviation-medium {
        color: #ffc107;
        font-weight: bold;
    }
    .deviation-low {
        color: #28a745;
        font-weight: bold;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-pie-chart me-2"></i>Price Collection Summary Reports
                            </h2>
                            <p class="text-muted mb-0">Comprehensive analysis and trends for the last 12 months</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-secondary">
                                <i class="bi bi-house me-1"></i> Back to Dashboard
                            </a>
                            <a href="<?= base_url('admin/price-collection-reports/raw') ?>" class="btn btn-info">
                                <i class="bi bi-file-earmark-bar-graph me-1"></i> Raw Data
                            </a>
                            <button id="exportSummary" class="btn btn-success">
                                <i class="bi bi-file-earmark-excel me-1"></i> Export Summary
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Info Boxes -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="info-box">
                <h3><?= number_format($inflationData['inflation_rate'], 2) ?>%</h3>
                <p>12-Month Inflation Rate</p>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="info-box">
                <h3><?= count($summaryTableData) ?></h3>
                <p>Goods Groups Analyzed</p>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="info-box">
                <h3><?= number_format($inflationData['total_records']) ?></h3>
                <p>Total Price Records</p>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="info-box">
                <h3><?= number_format($inflationData['current_avg_price'], 2) ?></h3>
                <p>Current Avg Price</p>
            </div>
        </div>
    </div>

    <!-- Summary Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-table me-2"></i>Goods Groups Summary
                        <span class="badge bg-primary ms-2"><?= count($summaryTableData) ?> Groups</span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($summaryTableData)): ?>
                        <div class="table-responsive">
                            <table id="summaryTable" class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Goods Group</th>
                                        <th>Average Price</th>
                                        <th>High Price</th>
                                        <th>Low Price</th>
                                        <th>Deviation</th>
                                        <th>Records</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $counter = 1; ?>
                                    <?php foreach ($summaryTableData as $row): ?>
                                        <tr>
                                            <td><?= $counter++ ?></td>
                                            <td><strong><?= esc($row['group_name']) ?></strong></td>
                                            <td class="text-end">
                                                <strong><?= number_format($row['avg_price'], 2) ?></strong>
                                            </td>
                                            <td class="text-end text-danger">
                                                <?= number_format($row['high_price'], 2) ?>
                                            </td>
                                            <td class="text-end text-success">
                                                <?= number_format($row['low_price'], 2) ?>
                                            </td>
                                            <td class="text-end">
                                                <?php 
                                                $deviation = $row['price_deviation'] ?? 0;
                                                $deviationClass = 'deviation-low';
                                                if ($deviation > 10) $deviationClass = 'deviation-high';
                                                elseif ($deviation > 5) $deviationClass = 'deviation-medium';
                                                ?>
                                                <span class="<?= $deviationClass ?>">
                                                    <?= number_format($deviation, 2) ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-info"><?= number_format($row['total_records']) ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            No price collection data available for the last 12 months.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- District Inflation Data -->
    <?php if (!empty($districtInflationData)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-geo-alt me-2"></i>District-wise Inflation Analysis
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead class="table-light">
                                <tr>
                                    <th>District</th>
                                    <th>Current Avg Price</th>
                                    <th>12 Months Ago</th>
                                    <th>Inflation Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($districtInflationData as $district): ?>
                                    <tr>
                                        <td><?= esc($district['district_name']) ?></td>
                                        <td class="text-end"><?= number_format($district['current_avg_price'], 2) ?></td>
                                        <td class="text-end"><?= number_format($district['old_avg_price'], 2) ?></td>
                                        <td class="text-end">
                                            <span class="badge bg-<?= $district['inflation_rate'] > 0 ? 'danger' : 'success' ?>">
                                                <?= number_format($district['inflation_rate'], 2) ?>%
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- High/Low Price Bar Chart -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-bar-chart me-2"></i>High vs Low Prices by Goods Group
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="highLowChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inflation Trend Line Chart -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up me-2"></i>12-Month Inflation Trend
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="inflationTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Price Trends Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up-arrow me-2"></i>Price Trends by Goods Group
                        </h5>
                        <div>
                            <select id="goodsGroupFilter" class="form-select form-select-sm" style="width: 200px;">
                                <option value="all">Show All Groups</option>
                                <?php foreach ($priceTrendData['goods_groups'] as $group): ?>
                                    <option value="<?= esc($group['label']) ?>"><?= esc($group['label']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 500px;">
                        <canvas id="priceTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#summaryTable').DataTable({
        "paging": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "responsive": true,
        "pageLength": 25,
        "order": [[ 2, "desc" ]],
        "dom": 'Bfrtip',
        "buttons": [
            {
                extend: 'excel',
                text: '<i class="bi bi-file-earmark-excel me-1"></i> Export Excel',
                className: 'btn btn-success btn-sm'
            }
        ],
        "language": {
            "search": "Search goods groups:",
            "info": "Showing _START_ to _END_ of _TOTAL_ goods groups",
            "infoEmpty": "No goods groups available",
            "infoFiltered": "(filtered from _MAX_ total groups)",
            "zeroRecords": "No matching goods groups found"
        }
    });

    // Prepare data for charts
    const summaryData = <?= json_encode($summaryTableData) ?>;
    const inflationTrendData = <?= json_encode($inflationTrendData) ?>;
    const priceTrendData = <?= json_encode($priceTrendData) ?>;

    // Chart.js default configuration
    Chart.defaults.font.family = 'Inter, sans-serif';
    Chart.defaults.color = '#6c757d';

    // High/Low Price Bar Chart
    const highLowCtx = document.getElementById('highLowChart').getContext('2d');
    const highLowChart = new Chart(highLowCtx, {
        type: 'bar',
        data: {
            labels: summaryData.map(item => item.group_name),
            datasets: [
                {
                    label: 'High Price',
                    data: summaryData.map(item => parseFloat(item.high_price)),
                    backgroundColor: 'rgba(220, 53, 69, 0.8)',
                    borderColor: 'rgba(220, 53, 69, 1)',
                    borderWidth: 1
                },
                {
                    label: 'Low Price',
                    data: summaryData.map(item => parseFloat(item.low_price)),
                    backgroundColor: 'rgba(40, 167, 69, 0.8)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Price Range Comparison by Goods Group'
                },
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Price'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Goods Groups'
                    }
                }
            }
        }
    });

    // Inflation Trend Line Chart
    const inflationCtx = document.getElementById('inflationTrendChart').getContext('2d');
    const inflationChart = new Chart(inflationCtx, {
        type: 'line',
        data: {
            labels: inflationTrendData.months,
            datasets: [{
                label: 'Inflation Rate (%)',
                data: inflationTrendData.inflation_rates,
                borderColor: 'rgba(0, 123, 255, 1)',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Monthly Inflation Rate Trend'
                },
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    title: {
                        display: true,
                        text: 'Inflation Rate (%)'
                    },
                    grid: {
                        color: function(context) {
                            if (context.tick.value === 0) {
                                return 'rgba(0, 0, 0, 0.3)';
                            }
                            return 'rgba(0, 0, 0, 0.1)';
                        }
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Month'
                    }
                }
            }
        }
    });

    // Price Trends Line Chart
    const priceTrendsCtx = document.getElementById('priceTrendsChart').getContext('2d');

    // Generate colors for each goods group
    const colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
        '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
    ];

    const priceTrendsDatasets = priceTrendData.goods_groups.map((group, index) => ({
        label: group.label,
        data: group.data,
        borderColor: colors[index % colors.length],
        backgroundColor: colors[index % colors.length] + '20',
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        spanGaps: true
    }));

    const priceTrendsChart = new Chart(priceTrendsCtx, {
        type: 'line',
        data: {
            labels: priceTrendData.months,
            datasets: priceTrendsDatasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Price Trends by Goods Group (12 Months)'
                },
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 15
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Average Price'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Month'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    // Goods Group Filter functionality
    $('#goodsGroupFilter').on('change', function() {
        const selectedGroup = $(this).val();

        if (selectedGroup === 'all') {
            // Show all datasets
            priceTrendsChart.data.datasets.forEach((dataset, index) => {
                dataset.hidden = false;
            });
        } else {
            // Hide all datasets except selected
            priceTrendsChart.data.datasets.forEach((dataset, index) => {
                dataset.hidden = dataset.label !== selectedGroup;
            });
        }

        priceTrendsChart.update();
    });

    // Export Summary functionality
    $('#exportSummary').on('click', function() {
        // Trigger DataTable export
        $('#summaryTable').DataTable().button('.buttons-excel').trigger();
    });
});
</script>
<?= $this->endSection() ?>
