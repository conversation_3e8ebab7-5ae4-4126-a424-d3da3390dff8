<?php

namespace App\Models;

use CodeIgniter\Model;

class BusinessLocationModel extends Model
{
    protected $table = 'business_locations';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'business_entity_id',
        'type',
        'business_name',
        'remarks',
        'country_id',
        'province_id',
        'district_id',
        'gps_coordinates',
        'status',
        'status_by',
        'status_at',
        'status_remarks',
        'created_by',
        'updated_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'business_entity_id' => 'required|integer',
        'type' => 'required|in_list[retail,wholesale]',
        'business_name' => 'required|max_length[150]',
        'country_id' => 'permit_empty|integer',
        'province_id' => 'permit_empty|integer',
        'district_id' => 'permit_empty|integer',
        'gps_coordinates' => 'permit_empty|max_length[200]',
        'status' => 'required|in_list[active,inactive]',
        'status_by' => 'permit_empty|integer',
        'created_by' => 'permit_empty|integer',
        'updated_by' => 'permit_empty|integer'
    ];
    
    protected $validationMessages = [
        'business_entity_id' => [
            'required' => 'Business entity is required.',
            'integer' => 'Invalid business entity selected.'
        ],
        'type' => [
            'required' => 'Location type is required.',
            'in_list' => 'Location type must be either retail or wholesale.'
        ],
        'business_name' => [
            'required' => 'Location name is required.',
            'max_length' => 'Location name cannot exceed 150 characters.'
        ],
        'status' => [
            'required' => 'Status is required.',
            'in_list' => 'Status must be either active or inactive.'
        ]
    ];
    
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    
    // Callbacks
    protected $beforeInsert = ['setCreatedBy', 'validateUniqueLocation'];
    protected $beforeUpdate = ['setUpdatedBy', 'validateUniqueLocation'];
    
    /**
     * Set created_by field before insert
     */
    protected function setCreatedBy(array $data)
    {
        if (!isset($data['data']['created_by']) && session()->has('user_id')) {
            $data['data']['created_by'] = session()->get('user_id');
        }
        return $data;
    }
    
    /**
     * Set updated_by field before update
     */
    protected function setUpdatedBy(array $data)
    {
        if (!isset($data['data']['updated_by']) && session()->has('user_id')) {
            $data['data']['updated_by'] = session()->get('user_id');
        }
        return $data;
    }
    
    /**
     * Validate unique location name under same business entity
     */
    protected function validateUniqueLocation(array $data)
    {
        if (isset($data['data']['business_entity_id']) && isset($data['data']['business_name'])) {
            $builder = $this->where('business_entity_id', $data['data']['business_entity_id'])
                           ->where('business_name', $data['data']['business_name'])
                           ->where('is_deleted', false);
            
            // For updates, exclude current record
            if (isset($data['id'])) {
                $builder->where('id !=', $data['id']);
            }
            
            $existing = $builder->first();
            if ($existing) {
                throw new \RuntimeException('Location name already exists under this business entity.');
            }
        }
        return $data;
    }
    
    /**
     * Get active business locations
     */
    public function getActive()
    {
        return $this->where('status', 'active')
                   ->where('is_deleted', false)
                   ->orderBy('business_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get locations by business entity
     */
    public function getByBusinessEntity($businessEntityId)
    {
        $builder = $this->db->table($this->table . ' bl')
                           ->select('bl.*, be.business_name as entity_name,
                                    gc.name as country, gp.name as province, gd.name as district')
                           ->join('business_entities be', 'bl.business_entity_id = be.id', 'left')
                           ->join('geo_countries gc', 'bl.country_id = gc.id', 'left')
                           ->join('geo_provinces gp', 'bl.province_id = gp.id', 'left')
                           ->join('geo_districts gd', 'bl.district_id = gd.id', 'left')
                           ->where('bl.business_entity_id', $businessEntityId)
                           ->where('bl.is_deleted', false)
                           ->orderBy('bl.business_name', 'ASC');

        return $builder->get()->getResultArray();
    }
    
    /**
     * Get locations with business entity details
     */
    public function getWithBusinessEntity($id = null)
    {
        $builder = $this->db->table($this->table . ' bl')
                           ->select('bl.*, be.business_name as entity_name,
                                    gc.name as country, gp.name as province, gd.name as district')
                           ->join('business_entities be', 'bl.business_entity_id = be.id', 'left')
                           ->join('geo_countries gc', 'bl.country_id = gc.id', 'left')
                           ->join('geo_provinces gp', 'bl.province_id = gp.id', 'left')
                           ->join('geo_districts gd', 'bl.district_id = gd.id', 'left')
                           ->where('bl.is_deleted', false);

        if ($id !== null) {
            $builder->where('bl.id', $id);
            return $builder->get()->getRowArray();
        }

        return $builder->orderBy('be.business_name', 'ASC')
                      ->orderBy('bl.business_name', 'ASC')
                      ->get()->getResultArray();
    }

    /**
     * Get all business locations with complete details for reporting
     */
    public function getReportData()
    {
        $builder = $this->db->table($this->table . ' bl')
                           ->select('bl.id as location_id,
                                    bl.business_name as location_name,
                                    bl.gps_coordinates,
                                    bl.status as location_status,
                                    bl.created_at as location_created_at,
                                    be.id as entity_id,
                                    be.business_name as entity_name,
                                    be.status as entity_status,
                                    gc.name as country,
                                    gp.name as province,
                                    gd.name as district')
                           ->join('business_entities be', 'bl.business_entity_id = be.id', 'left')
                           ->join('geo_countries gc', 'bl.country_id = gc.id', 'left')
                           ->join('geo_provinces gp', 'bl.province_id = gp.id', 'left')
                           ->join('geo_districts gd', 'bl.district_id = gd.id', 'left')
                           ->where('bl.is_deleted', false)
                           ->where('be.is_deleted', false);

        return $builder->orderBy('be.business_name', 'ASC')
                      ->orderBy('bl.business_name', 'ASC')
                      ->get()->getResultArray();
    }
    
    /**
     * Update status with tracking
     */
    public function updateStatus($id, $status, $remarks = null)
    {
        $data = [
            'status' => $status,
            'status_by' => session()->get('admin_user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'status_remarks' => $remarks
        ];
        
        return $this->update($id, $data);
    }
    
    /**
     * Soft delete with tracking
     */
    public function softDelete($id)
    {
        $data = [
            'is_deleted' => true,
            'deleted_by' => session()->get('admin_user_id'),
            'deleted_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->update($id, $data);
    }
    
    /**
     * Get locations grouped by country/province/district
     */
    public function getGroupedByLocation()
    {
        $builder = $this->db->table($this->table . ' bl')
                           ->select('gc.name as country, gp.name as province, gd.name as district, COUNT(*) as location_count')
                           ->join('geo_countries gc', 'bl.country_id = gc.id', 'left')
                           ->join('geo_provinces gp', 'bl.province_id = gp.id', 'left')
                           ->join('geo_districts gd', 'bl.district_id = gd.id', 'left')
                           ->where('bl.is_deleted', false)
                           ->where('bl.status', 'active')
                           ->groupBy(['gc.name', 'gp.name', 'gd.name'])
                           ->orderBy('gc.name, gp.name, gd.name');

        return $builder->get()->getResultArray();
    }
}
