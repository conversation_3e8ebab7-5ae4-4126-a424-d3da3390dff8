<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>
<div class="card">
    <h1 class="card-title">📋 Activities</h1>
    <p style="color:#666;margin-bottom:0;">Select an activity to collect prices</p>
</div>

<!-- Navigation -->
<div class="card" style="margin-top: 10px;">
    <a href="<?= base_url('field/dashboard') ?>" class="btn btn-secondary btn-block">← Back to Dashboard</a>
</div>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success"><?= session()->getFlashdata('success') ?></div>
<?php endif; ?>
<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger"><?= session()->getFlashdata('error') ?></div>
<?php endif; ?>

<?php if (empty($activities)): ?>
    <div class="card">
        <div style="text-align:center;padding:20px;color:#666;">
            <div style="font-size:48px;margin-bottom:10px;">🗂️</div>
            <h3>No Activities Found</h3>
            <p>You currently have no activities available for price collection.</p>
            <a href="<?= base_url('field/dashboard') ?>" class="btn">← Back to Dashboard</a>
        </div>
    </div>
<?php else: ?>
    <?php
    // Separate activities by status
    $activeActivities = [];
    $approvedActivities = [];

    foreach ($activities as $activity) {
        if (strtolower($activity['status']) === 'approved') {
            $approvedActivities[] = $activity;
        } else {
            $activeActivities[] = $activity;
        }
    }
    ?>

    <!-- Active Activities -->
    <?php if (!empty($activeActivities)): ?>
        <?php foreach ($activeActivities as $activity): ?>
            <div class="card" style="margin-bottom:12px;">
                <div style="display:flex;justify-content:space-between;align-items:flex-start;">
                    <div>
                        <h3 style="margin:0;font-size:16px;color:#333;"><?= esc($activity['activity_name']) ?></h3>
                        <div style="color:#666;font-size:14px;">
                            📊 Type: <?= esc($activity['activity_type']) ?><br>
                            📅 From: <?= date('M j, Y', strtotime($activity['date_from'])) ?>
                            &nbsp;→&nbsp;
                            <?= date('M j, Y', strtotime($activity['date_to'])) ?>
                            <br>
                            🏷️ Status: <strong><?= esc(ucfirst($activity['status'])) ?></strong><br>
                            🏪 Locations: <strong><?= (int)($activity['location_count'] ?? 0) ?></strong>
                            &nbsp;&nbsp;👥 Users: <strong><?= (int)($activity['user_count'] ?? 0) ?></strong>
                        </div>
                    </div>
                    <div>
                        <a class="btn btn-success" href="<?= base_url('field/collect/activity/' . $activity['id']) ?>">Open</a>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Approved Activities Section -->
    <?php if (!empty($approvedActivities)): ?>
        <div class="card" style="margin-top:30px;">
            <h2 class="card-title" style="color:#28a745;">✅ Approved Activities</h2>
            <p style="color:#666;margin-bottom:15px;">These activities have been approved and are read-only.</p>
        </div>

        <?php foreach ($approvedActivities as $activity): ?>
            <div class="card" style="margin-bottom:12px;background:#f8fff9;border-left:4px solid #28a745;">
                <div style="display:flex;justify-content:space-between;align-items:flex-start;">
                    <div>
                        <h3 style="margin:0;font-size:16px;color:#333;"><?= esc($activity['activity_name']) ?></h3>
                        <div style="color:#666;font-size:14px;">
                            📊 Type: <?= esc($activity['activity_type']) ?><br>
                            📅 From: <?= date('M j, Y', strtotime($activity['date_from'])) ?>
                            &nbsp;→&nbsp;
                            <?= date('M j, Y', strtotime($activity['date_to'])) ?>
                            <br>
                            🏷️ Status: <strong style="color:#28a745;"><?= esc(ucfirst($activity['status'])) ?></strong><br>
                            🏪 Locations: <strong><?= (int)($activity['location_count'] ?? 0) ?></strong>
                            &nbsp;&nbsp;👥 Users: <strong><?= (int)($activity['user_count'] ?? 0) ?></strong>
                        </div>
                    </div>
                    <div>
                        <a class="btn btn-success" href="<?= base_url('field/collect/activity/' . $activity['id']) ?>">View</a>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
<?php endif; ?>

<?= $this->endSection() ?>

