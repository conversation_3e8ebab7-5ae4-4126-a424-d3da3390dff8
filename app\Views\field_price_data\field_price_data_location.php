<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>
<div class="card">
    <h1 class="card-title">💰 Collect Prices</h1>
    <p style="color:#666;margin-bottom:0;">
        Activity: <strong><?= esc($activity['activity_name']) ?></strong>
        <br>Location: <strong><?= esc($location['business_name']) ?></strong>
    </p>
</div>

<!-- Navigation -->
<div class="card" style="margin-top: 10px; display: flex; gap: 10px;">
    <a class="btn btn-secondary" href="<?= base_url('field/collect/activity/' . $activity['id']) ?>">← Back to Locations</a>
    <a class="btn" href="<?= base_url('field/collect') ?>">Activities</a>
</div>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success"><?= session()->getFlashdata('success') ?></div>
<?php endif; ?>
<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger"><?= session()->getFlashdata('error') ?></div>
<?php endif; ?>
<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger">
        <?php foreach (session()->getFlashdata('errors') as $err): ?>
            <div>• <?= esc($err) ?></div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<div class="card">
    <h2 class="card-title">Items (Primary/Substitute)</h2>
    <div class="table-responsive" style="width: 100%;">
        <table class="table table-hover" style="width: 100%;">
            <thead class="table-dark">
                <tr>
                    <th style="width: 60px;">#</th>
                    <th>Item</th>
                    <th style="width: 140px;">Type</th>
                    <th style="width: 120px;">Price</th>
                    <th>Remarks</th>
                    <th style="width: 120px;">Action</th>
                </tr>
            </thead>
            <tbody class="table-light">
                <?php foreach ($items as $index => $it): ?>
                    <?php $existing = $entriesByItem[$it['id']] ?? null; ?>
                    <tr>
                        <td><?= $index + 1 ?></td>
                        <td>
                            <div style="line-height: 1.4;">
                                <div style="font-size: 12px; color: #666; margin-bottom: 2px;">
                                    <strong><?= esc($it['group_name'] ?? 'Unknown Group') ?></strong> → <?= esc($it['brand_name'] ?? 'Unknown Brand') ?>
                                </div>
                                <div style="font-weight: bold; color: #333;">
                                    <?= esc($it['item']) ?>
                                </div>
                            </div>
                        </td>
                        <td>
                            <?php $t = strtolower($it['brand_type'] ?? ''); ?>
                            <span class="badge" style="background:<?= $t==='primary'?'#0d6efd':'#6c757d' ?>;"><?= $t==='primary'?'Primary':'Substitute' ?></span>
                        </td>
                        <?php if ($existing): ?>
                            <td><strong><?= number_format((float)$existing['price'], 2) ?></strong></td>
                            <td><?= esc($existing['remarks'] ?? '') ?></td>
                            <td>
                                <?php if (strtolower($activity['status']) !== 'approved'): ?>
                                    <button class="btn btn-sm edit-btn"
                                            data-entry-id="<?= $existing['id'] ?>"
                                            data-item-name="<?= esc($it['group_name'] ?? 'Unknown Group') ?> → <?= esc($it['brand_name'] ?? 'Unknown Brand') ?> → <?= esc($it['item']) ?>"
                                            data-price="<?= $existing['price'] ?>"
                                            data-remarks="<?= esc($existing['remarks'] ?? '') ?>">Edit</button>
                                <?php else: ?>
                                    <span class="badge" style="background:#28a745;">Approved</span>
                                <?php endif; ?>
                            </td>
                        <?php else: ?>
                            <td>-</td>
                            <td>-</td>
                            <td>
                                <?php if (strtolower($activity['status']) !== 'approved'): ?>
                                    <button class="btn btn-success btn-sm create-btn"
                                            data-item-id="<?= $it['id'] ?>"
                                            data-item-name="<?= esc($it['group_name'] ?? 'Unknown Group') ?> → <?= esc($it['brand_name'] ?? 'Unknown Brand') ?> → <?= esc($it['item']) ?>">Enter Data</button>
                                <?php else: ?>
                                    <span class="text-muted">No data</span>
                                <?php endif; ?>
                            </td>
                        <?php endif; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<div class="card">
    <h2 class="card-title">Existing Entries</h2>
    <?php if (empty($entries)): ?>
        <div class="text-muted">No entries yet.</div>
    <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Item</th>
                        <th>Price</th>
                        <th>Remarks</th>
                        <th>Created</th>
                        <th>Created By</th>
                    </tr>
                </thead>
                <tbody class="table-light">
                    <?php foreach ($entries as $i => $row): ?>
                        <tr>
                            <td><?= $i + 1 ?></td>
                            <td>
                                <div style="line-height: 1.4;">
                                    <div style="font-size: 12px; color: #666; margin-bottom: 2px;">
                                        <strong><?= esc($row['group_name'] ?? 'Unknown Group') ?></strong> → <?= esc($row['brand_name'] ?? 'Unknown Brand') ?>
                                    </div>
                                    <div style="font-weight: bold; color: #333;">
                                        <?= esc($row['item_name'] ?? '') ?>
                                    </div>
                                </div>
                            </td>
                            <td><strong><?= number_format((float)$row['price'], 2) ?></strong></td>
                            <td><?= esc($row['remarks'] ?? '') ?></td>
                            <td><?= date('M j, Y H:i', strtotime($row['created_at'])) ?></td>
                            <td><?= esc($row['created_by_name'] ?? 'Unknown') ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<!-- Create Price Entry Modal -->
<div id="createModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="createModalTitle">Enter Price Data</h3>
            <button type="button" class="modal-close" onclick="closeModal('createModal')">&times;</button>
        </div>
        <form method="post" action="<?= base_url('field/collect/create') ?>">
            <div class="modal-body">
                <input type="hidden" name="activity_id" value="<?= esc($activity['id']) ?>">
                <input type="hidden" name="business_location_id" value="<?= esc($location['id']) ?>">
                <input type="hidden" name="item_id" id="createItemId">

                <div class="form-group">
                    <label class="form-label">Price</label>
                    <input type="number" name="price" step="0.01" min="0" class="form-control" placeholder="0.00" required>
                </div>

                <div class="form-group">
                    <label class="form-label">Remarks</label>
                    <textarea name="remarks" rows="2" class="form-control" placeholder="Optional notes..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('createModal')">Cancel</button>
                <button type="submit" class="btn btn-success">Save</button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Price Entry Modal -->
<div id="editModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="editModalTitle">Edit Price Data</h3>
            <button type="button" class="modal-close" onclick="closeModal('editModal')">&times;</button>
        </div>
        <form method="post" id="editForm">
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Price</label>
                    <input type="number" name="price" step="0.01" min="0" class="form-control" id="editPrice" required>
                </div>

                <div class="form-group">
                    <label class="form-label">Remarks</label>
                    <textarea name="remarks" rows="2" class="form-control" id="editRemarks" placeholder="Optional notes..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('editModal')">Cancel</button>
                <button type="submit" class="btn btn-success">Update</button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

