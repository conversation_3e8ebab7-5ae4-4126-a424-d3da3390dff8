<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Admin Portal Routes
$routes->get('admin', 'AdminPortal::index');
$routes->post('admin/authenticate', 'AdminPortal::authenticate');
$routes->get('admin/dashboard', 'AdminPortal::dashboard');
$routes->get('admin/forgot-password', 'AdminPortal::forgotPassword');
$routes->post('admin/forgot-password', 'AdminPortal::processForgotPassword');
$routes->get('admin/logout', 'AdminPortal::logout');

// Admin Profile Routes
$routes->get('admin/profile', 'AdminProfile::index');
$routes->post('admin/profile/update', 'AdminProfile::update');
$routes->post('admin/profile/change-password', 'AdminProfile::changePassword');

// Admin Users CRUD Routes
$routes->get('admin/users', 'AdminUsers::index');
$routes->get('admin/users/new', 'AdminUsers::new');
$routes->post('admin/users/create', 'AdminUsers::create');
$routes->get('admin/users/(:num)', 'AdminUsers::show/$1');
$routes->get('admin/users/(:num)/edit', 'AdminUsers::edit/$1');
$routes->post('admin/users/(:num)/update', 'AdminUsers::update/$1');

// Business Entities CRUD Routes
$routes->get('admin/business-entities', 'BusinessEntities::index');
$routes->get('admin/business-entities/new', 'BusinessEntities::new');
$routes->post('admin/business-entities/create', 'BusinessEntities::create');
$routes->get('admin/business-entities/(:num)', 'BusinessEntities::show/$1');
$routes->get('admin/business-entities/(:num)/edit', 'BusinessEntities::edit/$1');
$routes->post('admin/business-entities/(:num)/update', 'BusinessEntities::update/$1');
$routes->post('admin/business-entities/(:num)/delete', 'BusinessEntities::delete/$1');

// Business Entities Report Routes
$routes->get('admin/business-entities-report', 'BusinessEntitiesReport::index');

// Business Locations CRUD Routes
$routes->get('admin/business-locations', 'BusinessLocations::index');
$routes->get('admin/business-locations/new', 'BusinessLocations::new');
$routes->post('admin/business-locations/create', 'BusinessLocations::create');
$routes->get('admin/business-locations/(:num)', 'BusinessLocations::show/$1');
$routes->get('admin/business-locations/(:num)/edit', 'BusinessLocations::edit/$1');
$routes->post('admin/business-locations/(:num)/update', 'BusinessLocations::update/$1');
$routes->post('admin/business-locations/(:num)/delete', 'BusinessLocations::delete/$1');

// AJAX routes for geo dependency dropdowns
$routes->post('admin/business-locations/get-provinces', 'BusinessLocations::getProvincesByCountry');
$routes->post('admin/business-locations/get-districts', 'BusinessLocations::getDistrictsByProvince');

// Admin Goods Report Route
$routes->get('admin/goods-report', 'GoodsReport::index');

// Price Collection Reports Routes
$routes->get('admin/price-collection-reports/raw', 'PriceCollectionReports::raw');
$routes->get('admin/price-collection-reports/summary', 'PriceCollectionReports::summary');

// Field Portal Routes
$routes->get('field', 'FieldPortal::index');
$routes->get('field/dashboard', 'FieldPortal::dashboard');

// Field Price Collection (RESTful, no AJAX)
$routes->get('field/collect', 'FieldPriceData::index');
$routes->get('field/collect/activity/(:num)', 'FieldPriceData::activity/$1');

$routes->get('field/collect/activity/(:num)/location/(:num)', 'FieldPriceData::location/$1/$2');
$routes->post('field/collect/create', 'FieldPriceData::create');
$routes->get('field/collect/edit/(:num)', 'FieldPriceData::edit/$1');
$routes->post('field/collect/update/(:num)', 'FieldPriceData::update/$1');

$routes->get('field/reports', 'FieldPortal::reports');
$routes->get('field/profile', 'FieldPortal::profile');
$routes->post('field/profile/update', 'FieldPortal::updateProfile');
$routes->post('field/profile/change-password', 'FieldPortal::changePassword');

$routes->get('field/logout', 'FieldPortal::logout');

// Field Tasks Routes (RESTful)
$routes->get('field/tasks', 'FieldTasks::index');
$routes->get('field/tasks/view/(:num)', 'FieldTasks::show/$1');
$routes->post('field/tasks/update-status', 'FieldTasks::updateStatus');

// Removed legacy AJAX submit route to enforce standard form submission

// Dakoii Panel Routes
$routes->get('dakoii', 'Dakoii::index');
$routes->post('dakoii/authenticate', 'Dakoii::authenticate');
$routes->get('dakoii/dashboard', 'Dakoii::dashboard');
$routes->get('dakoii/logout', 'Dakoii::logout');

// Dakoii Users CRUD Routes
$routes->get('dakoii/users', 'DakoiiUsers::index');
$routes->get('dakoii/users/new', 'DakoiiUsers::new');
$routes->post('dakoii/users/create', 'DakoiiUsers::create');
$routes->get('dakoii/users/(:num)', 'DakoiiUsers::show/$1');
$routes->get('dakoii/users/(:num)/edit', 'DakoiiUsers::edit/$1');
$routes->post('dakoii/users/(:num)/update', 'DakoiiUsers::update/$1');
$routes->post('dakoii/users/(:num)/delete', 'DakoiiUsers::delete/$1');

// Dakoii Organizations CRUD Routes
$routes->get('dakoii/organizations', 'DakoiiOrganizations::index');
$routes->get('dakoii/organizations/new', 'DakoiiOrganizations::new');
$routes->post('dakoii/organizations/create', 'DakoiiOrganizations::create');
$routes->get('dakoii/organizations/(:num)', 'DakoiiOrganizations::show/$1');
$routes->get('dakoii/organizations/(:num)/edit', 'DakoiiOrganizations::edit/$1');
$routes->post('dakoii/organizations/(:num)/update', 'DakoiiOrganizations::update/$1');
$routes->post('dakoii/organizations/(:num)/delete', 'DakoiiOrganizations::delete/$1');
$routes->get('dakoii/organizations/provinces/(:num)', 'DakoiiOrganizations::getProvincesByCountry/$1');

// Organization Users CRUD Routes
$routes->get('dakoii/organization-users', 'OrganizationUsers::index');
$routes->get('dakoii/organization-users/new', 'OrganizationUsers::new');
$routes->post('dakoii/organization-users/create', 'OrganizationUsers::create');
$routes->get('dakoii/organization-users/(:num)', 'OrganizationUsers::show/$1');
$routes->get('dakoii/organization-users/(:num)/edit', 'OrganizationUsers::edit/$1');
$routes->post('dakoii/organization-users/(:num)/update', 'OrganizationUsers::update/$1');
$routes->post('dakoii/organization-users/(:num)/delete', 'OrganizationUsers::delete/$1');

// System Users CRUD Routes (for main user management)
$routes->get('dakoii/system-users', 'Users::index');
$routes->get('dakoii/system-users/new', 'Users::new');
$routes->post('dakoii/system-users/create', 'Users::create');
$routes->get('dakoii/system-users/(:num)', 'Users::show/$1');
$routes->get('dakoii/system-users/(:num)/edit', 'Users::edit/$1');
$routes->post('dakoii/system-users/(:num)/update', 'Users::update/$1');
$routes->post('dakoii/system-users/(:num)/delete', 'Users::delete/$1');

// Account activation route
$routes->get('dakoii/activate-account/(:any)', 'Users::activateAccount/$1');

// AJAX email checking route
$routes->post('dakoii/check-email', 'Users::checkEmail');

// Admin Goods Management Routes
// Goods Groups CRUD Routes
$routes->get('admin/goods-groups', 'GoodsGroups::index');
$routes->get('admin/goods-groups/new', 'GoodsGroups::new');
$routes->post('admin/goods-groups/create', 'GoodsGroups::create');
$routes->get('admin/goods-groups/(:num)', 'GoodsGroups::show/$1');
$routes->get('admin/goods-groups/(:num)/edit', 'GoodsGroups::edit/$1');
$routes->post('admin/goods-groups/(:num)/update', 'GoodsGroups::update/$1');
$routes->post('admin/goods-groups/(:num)/delete', 'GoodsGroups::delete/$1');

// Goods Brands CRUD Routes
$routes->get('admin/goods-brands', 'GoodsBrands::index');
$routes->get('admin/goods-brands/new', 'GoodsBrands::new');
$routes->post('admin/goods-brands/create', 'GoodsBrands::create');
$routes->get('admin/goods-brands/(:num)', 'GoodsBrands::show/$1');
$routes->get('admin/goods-brands/(:num)/edit', 'GoodsBrands::edit/$1');
$routes->post('admin/goods-brands/(:num)/update', 'GoodsBrands::update/$1');
$routes->post('admin/goods-brands/(:num)/delete', 'GoodsBrands::delete/$1');

// Goods Items CRUD Routes
$routes->get('admin/goods-items', 'GoodsItems::index');
$routes->get('admin/goods-items/new', 'GoodsItems::new');
$routes->post('admin/goods-items/create', 'GoodsItems::create');
$routes->get('admin/goods-items/(:num)', 'GoodsItems::show/$1');
$routes->get('admin/goods-items/(:num)/edit', 'GoodsItems::edit/$1');
$routes->post('admin/goods-items/(:num)/update', 'GoodsItems::update/$1');
$routes->post('admin/goods-items/(:num)/delete', 'GoodsItems::delete/$1');
$routes->post('admin/goods-items/get-brands-by-group', 'GoodsItems::getBrandsByGroup');

// Workplans CRUD Routes
$routes->get('admin/workplans', 'Workplans::index');
$routes->get('admin/workplans/new', 'Workplans::new');
$routes->post('admin/workplans/create', 'Workplans::create');
$routes->get('admin/workplans/(:num)', 'Workplans::show/$1');
$routes->get('admin/workplans/(:num)/edit', 'Workplans::edit/$1');
$routes->post('admin/workplans/(:num)/update', 'Workplans::update/$1');
$routes->post('admin/workplans/(:num)/delete', 'Workplans::delete/$1');

// Activities CRUD Routes
$routes->get('admin/activities', 'Activities::index');
$routes->get('admin/activities/new', 'Activities::new');
$routes->post('admin/activities/create', 'Activities::create');
$routes->get('admin/activities/(:num)', 'Activities::show/$1');
$routes->get('admin/activities/(:num)/edit', 'Activities::edit/$1');
$routes->post('admin/activities/(:num)/update', 'Activities::update/$1');
$routes->post('admin/activities/(:num)/delete', 'Activities::delete/$1');

// Activity Assignment Routes
$routes->post('admin/activities/(:num)/assign-user', 'Activities::assignUser/$1');
$routes->post('admin/activities/(:num)/remove-user/(:num)', 'Activities::removeUser/$1/$2');
$routes->post('admin/activities/(:num)/assign-business', 'Activities::assignBusiness/$1');
$routes->post('admin/activities/(:num)/remove-business/(:num)', 'Activities::removeBusiness/$1/$2');

// Activity Status Change Routes
$routes->post('admin/activities/(:num)/resend', 'Activities::resend/$1');
$routes->post('admin/activities/(:num)/approve', 'Activities::approve/$1');
