Showing create queries
Tables
Table	Create table
activities	CREATE TABLE `activities` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `org_id` bigint(20) unsigned DEFAULT NULL,
 `workplan_id` bigint(20) unsigned DEFAULT NULL,
 `activity_type` varchar(255) DEFAULT NULL,
 `activity_name` varchar(255) NOT NULL,
 `remarks` text DEFAULT NULL,
 `date_from` date DEFAULT NULL,
 `date_to` date DEFAULT NULL,
 `status` enum('planned','active','completed','cancelled','suspended') DEFAULT NULL,
 `status_by` bigint(20) unsigned DEFAULT NULL,
 `status_at` timestamp NULL DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `deleted_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `created_at` timestamp NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`),
 KEY `idx_activities_org_id` (`org_id`),
 KEY `idx_activities_workplan_id` (`workplan_id`),
 KEY `idx_activities_status` (`status`),
 KEY `idx_activities_date_from_to` (`date_from`,`date_to`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
activity_business_locations	CREATE TABLE `activity_business_locations` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `activity_id` int(11) NOT NULL,
 `business_location_id` int(11) NOT NULL,
 `assigned_at` datetime DEFAULT NULL,
 `created_at` timestamp NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`),
 KEY `idx_activity_business_locations_org_id` (`org_id`),
 KEY `idx_activity_business_locations_activity_id` (`activity_id`),
 KEY `idx_activity_business_locations_business_location_id` (`business_location_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
activity_price_collection_data	CREATE TABLE `activity_price_collection_data` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `business_location_id` int(11) NOT NULL,
 `activity_id` int(11) NOT NULL,
 `user_id` int(11) NOT NULL,
 `item_id` int(11) NOT NULL,
 `price` decimal(10,2) NOT NULL,
 `remarks` text DEFAULT NULL,
 `status` enum('active','submitted','approved','redo','cancelled') NOT NULL DEFAULT 'active',
 `status_by` int(11) DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `created_at` timestamp NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `delete_by` int(11) DEFAULT NULL,
 `is_deleted` tinyint(4) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_activity_price_collection_org_id` (`org_id`),
 KEY `idx_activity_price_collection_activity_id` (`activity_id`),
 KEY `idx_activity_price_collection_user_id` (`user_id`),
 KEY `idx_activity_price_collection_item_id` (`item_id`),
 KEY `idx_activity_price_collection_business_location_id` (`business_location_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
activity_users	CREATE TABLE `activity_users` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `activity_id` int(11) NOT NULL,
 `user_id` int(11) NOT NULL,
 `assigned_at` datetime DEFAULT NULL,
 `created_at` timestamp NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`),
 KEY `idx_activity_users_org_id` (`org_id`),
 KEY `idx_activity_users_activity_id` (`activity_id`),
 KEY `idx_activity_users_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
business_entities	CREATE TABLE `business_entities` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `business_name` varchar(150) NOT NULL,
 `remarks` text DEFAULT NULL,
 `status` enum('active','inactive') NOT NULL DEFAULT 'active',
 `status_by` int(11) DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `deleted_by` int(11) DEFAULT NULL,
 `created_at` timestamp NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
business_locations	CREATE TABLE `business_locations` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `business_entity_id` int(11) NOT NULL,
 `business_name` varchar(150) NOT NULL DEFAULT '',
 `remarks` text DEFAULT NULL,
 `country_id` int(11) DEFAULT NULL,
 `province_id` int(11) DEFAULT NULL,
 `district_id` int(11) DEFAULT NULL,
 `gps_coordinates` varchar(200) DEFAULT NULL,
 `status` enum('active','inactive') NOT NULL DEFAULT 'active',
 `status_by` int(11) DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `deleted_by` int(11) DEFAULT NULL,
 `created_at` timestamp NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
dakoii_org	CREATE TABLE `dakoii_org` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `org_code` varchar(100) NOT NULL,
 `org_name` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `province_id` int(11) DEFAULT NULL,
 `country_id` int(11) DEFAULT NULL,
 `logo_path` varchar(255) DEFAULT NULL,
 `is_locationlocked` tinyint(1) NOT NULL DEFAULT 0,
 `postal_address` text DEFAULT NULL,
 `phone_numbers` text DEFAULT NULL,
 `email_addresses` text DEFAULT NULL,
 `is_active` tinyint(1) NOT NULL DEFAULT 1,
 `license_status` varchar(50) DEFAULT NULL,
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_by` int(11) unsigned DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `is_deleted` tinyint(1) DEFAULT 0,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
dakoii_users	CREATE TABLE `dakoii_users` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `username` varchar(255) NOT NULL,
 `password` varchar(255) NOT NULL,
 `role` varchar(100) NOT NULL,
 `is_active` tinyint(1) DEFAULT 0,
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_by` int(11) unsigned DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `is_deleted` tinyint(1) DEFAULT 0,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
flyway_schema_history	CREATE TABLE `flyway_schema_history` (
 `installed_rank` int(11) NOT NULL,
 `version` varchar(50) DEFAULT NULL,
 `description` varchar(200) NOT NULL,
 `type` varchar(20) NOT NULL,
 `script` varchar(1000) NOT NULL,
 `checksum` int(11) DEFAULT NULL,
 `installed_by` varchar(100) NOT NULL,
 `installed_on` timestamp NOT NULL DEFAULT current_timestamp(),
 `execution_time` int(11) NOT NULL,
 `success` tinyint(1) NOT NULL,
 PRIMARY KEY (`installed_rank`),
 KEY `flyway_schema_history_s_idx` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
geo_countries	CREATE TABLE `geo_countries` (
 `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
 `name` varchar(100) NOT NULL,
 `country_code` char(2) NOT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `created_by` int(10) unsigned DEFAULT NULL,
 `updated_by` int(10) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `uq_country_code` (`country_code`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
geo_districts	CREATE TABLE `geo_districts` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `district_code` varchar(10) DEFAULT NULL,
 `province_id` int(11) DEFAULT NULL,
 `json_id` varchar(50) DEFAULT NULL,
 `created_by` int(10) unsigned DEFAULT NULL,
 `updated_by` int(10) unsigned DEFAULT NULL,
 `name` varchar(255) NOT NULL,
 `country_id` int(11) NOT NULL,
 `created_at` timestamp NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`),
 KEY `idx_geo_districts_country_id` (`country_id`),
 KEY `idx_geo_districts_province_id` (`province_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
geo_provinces	CREATE TABLE `geo_provinces` (
 `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
 `province_code` varchar(10) NOT NULL,
 `name` varchar(100) NOT NULL,
 `country_id` int(10) unsigned NOT NULL,
 `json_id` varchar(50) DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `created_by` int(10) unsigned DEFAULT NULL,
 `updated_by` int(10) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `uq_province_code` (`province_code`),
 KEY `idx_country_id` (`country_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
goods_brands	CREATE TABLE `goods_brands` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `goods_group_id` int(11) NOT NULL,
 `brand_name` varchar(150) NOT NULL,
 `type` enum('primary','substitute') NOT NULL DEFAULT 'primary',
 `status` enum('active','inactive') NOT NULL DEFAULT 'active',
 `status_by` int(11) DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `deleted_by` int(11) DEFAULT NULL,
 `created_at` timestamp NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
goods_groups	CREATE TABLE `goods_groups` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `group_name` varchar(100) NOT NULL,
 `description` text DEFAULT NULL,
 `status` enum('active','inactive') NOT NULL DEFAULT 'active',
 `status_by` int(11) DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `deleted_by` int(11) DEFAULT NULL,
 `created_at` timestamp NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
goods_items	CREATE TABLE `goods_items` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `item` varchar(200) NOT NULL,
 `goods_group_id` int(11) NOT NULL,
 `goods_brand_id` int(11) NOT NULL,
 `remarks` text DEFAULT NULL,
 `status` enum('active','inactive') NOT NULL DEFAULT 'active',
 `status_by` int(11) DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `deleted_by` int(11) DEFAULT NULL,
 `created_at` timestamp NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
price_data	CREATE TABLE `price_data` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `org_id` bigint(20) unsigned DEFAULT NULL,
 `user_id` bigint(20) unsigned DEFAULT NULL,
 `activity_id` int(11) NOT NULL,
 `business_location_id` int(11) NOT NULL,
 `item_id` bigint(20) unsigned NOT NULL,
 `is_active` tinyint(1) DEFAULT 1,
 `effective_date` date NOT NULL,
 `price_amount` decimal(10,2) NOT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 `created_at` timestamp NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` timestamp NULL DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_price_data_date` (`effective_date`),
 KEY `idx_price_data_location` (`business_location_id`),
 KEY `idx_price_data_item` (`item_id`),
 KEY `idx_price_data_org_id` (`org_id`),
 KEY `idx_price_data_user_id` (`user_id`),
 KEY `idx_price_data_activity_id` (`activity_id`),
 KEY `idx_price_data_business_location_id` (`business_location_id`),
 KEY `idx_price_data_item_id` (`item_id`),
 KEY `idx_price_data_effective_date` (`effective_date`),
 KEY `idx_price_data_is_active` (`is_active`),
 KEY `idx_price_data_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
users	CREATE TABLE `users` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `sys_no` int(20) NOT NULL COMMENT 'system number',
 `name` varchar(255) NOT NULL,
 `password` varchar(255) NOT NULL,
 `role` enum('user','guest') NOT NULL DEFAULT 'user',
 `is_admin` tinyint(5) NOT NULL,
 `is_supervisor` tinyint(5) NOT NULL,
 `reports_to` int(11) DEFAULT NULL,
 `position` varchar(255) DEFAULT NULL,
 `id_photo` varchar(500) DEFAULT NULL,
 `phone` varchar(200) DEFAULT NULL,
 `email` varchar(500) NOT NULL,
 `status` varchar(20) NOT NULL,
 `activation_token` varchar(255) DEFAULT NULL,
 `activation_sent_at` datetime DEFAULT NULL,
 `activated_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `deleted_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
workplans	CREATE TABLE `workplans` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `org_id` bigint(20) unsigned DEFAULT NULL,
 `supervisor_id` bigint(20) unsigned DEFAULT NULL,
 `title` varchar(255) DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `date_from` date DEFAULT NULL,
 `date_to` date DEFAULT NULL,
 `status` enum('draft','approved','active','completed','cancelled') DEFAULT 'draft',
 `status_by` bigint(20) unsigned DEFAULT NULL,
 `status_at` timestamp NULL DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `created_at` timestamp NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Soft delete flag: 0=active, 1=deleted',
 `deleted_at` timestamp NULL DEFAULT NULL COMMENT 'Soft delete timestamp',
 PRIMARY KEY (`id`),
 KEY `idx_workplans_org_id` (`org_id`),
 KEY `idx_workplans_supervisor_id` (`supervisor_id`),
 KEY `idx_workplans_status` (`status`),
 KEY `idx_workplans_date_from_to` (`date_from`,`date_to`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci