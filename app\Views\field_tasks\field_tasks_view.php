<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="card">
    <h1 class="card-title">📋 Task Details</h1>
    <p style="color: #666; margin-bottom: 0;">View task information and update status</p>
</div>

<!-- Navigation -->
<div class="card" style="margin-top: 10px;">
    <a href="<?= base_url('field/tasks') ?>" class="btn btn-secondary btn-block">← Back to My Tasks</a>
</div>

<!-- Assignments -->
<div class="card">
    <h2 class="card-title">Assignments</h2>
    <div class="row">
        <div class="col col-6">
            <h3 class="card-title" style="font-size:16px;">Users Assigned</h3>
            <?php if (empty($users_assigned)): ?>
                <div class="text-muted">No users assigned.</div>
            <?php else: ?>
                <ul style="margin-left:18px;">
                    <?php foreach ($users_assigned as $ua): ?>
                        <li><?= esc($ua['name'] ?? 'User') ?> <span class="text-muted" style="font-size:12px;">(<?= esc($ua['email'] ?? '') ?>)</span></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
        <div class="col col-6">
            <h3 class="card-title" style="font-size:16px;">Locations Assigned</h3>
            <?php if (empty($locations_assigned)): ?>
                <div class="text-muted">No locations assigned.</div>
            <?php else: ?>
                <ul style="margin-left:18px;">
                    <?php foreach ($locations_assigned as $la): ?>
                        <li><?= esc($la['business_name'] ?? 'Location') ?> <span class="text-muted" style="font-size:12px;">(<?= esc($la['gps_coordinates'] ?? '') ?>)</span></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Flash messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success">
        <?= session()->getFlashdata('success') ?>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger">
        <?= session()->getFlashdata('error') ?>
    </div>
<?php endif; ?>

<!-- Task Information -->
<div class="card">
    <h2 class="card-title">📊 Task Information</h2>
    
    <div class="form-group">
        <label class="form-label">Activity Name</label>
        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; font-weight: bold;">
            <?= esc($task['activity_name']) ?>
        </div>
    </div>
    
    <div class="form-group">
        <label class="form-label">Activity Type</label>
        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px;">
            <?= esc($task['activity_type']) ?>
        </div>
    </div>
    
    <div class="row">
        <div class="col col-6">
            <div class="form-group">
                <label class="form-label">Start Date</label>
                <div style="background: #f8f9fa; padding: 12px; border-radius: 6px;">
                    <?= date('M j, Y', strtotime($task['date_from'])) ?>
                </div>
            </div>
        </div>
        <div class="col col-6">
            <div class="form-group">
                <label class="form-label">End Date</label>
                <div style="background: #f8f9fa; padding: 12px; border-radius: 6px;">
                    <?= date('M j, Y', strtotime($task['date_to'])) ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="form-group">
        <label class="form-label">Current Status</label>
        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px;">
            <?php
            $statusText = '';
            $statusColor = '';
            
            switch ($task['status']) {
                case 'active':
                    $statusText = '🟢 Active';
                    $statusColor = '#28a745';
                    break;
                case 'completed':
                    $statusText = '✅ Completed';
                    $statusColor = '#28a745';
                    break;
                case 'approved':
                    $statusText = '🎉 Approved';
                    $statusColor = '#28a745';
                    break;
                case 'planned':
                    $statusText = '📋 Planned';
                    $statusColor = '#007bff';
                    break;
                default:
                    $statusText = '⏳ Pending';
                    $statusColor = '#6c757d';
                    break;
            }
            ?>
            <span style="color: <?= $statusColor ?>; font-weight: bold;"><?= $statusText ?></span>

            <?php if (!empty($task['status_at'])): ?>
                <div style="margin-top: 8px; font-size: 12px; color: #666;">
                    Updated: <?= date('M j, Y g:i A', strtotime($task['status_at'])) ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <?php if (!empty($task['status_remarks'])): ?>
    <div class="form-group">
        <label class="form-label">Status Remarks</label>
        <div style="background: #fff3cd; padding: 12px; border-radius: 6px; border-left: 4px solid #ffc107;">
            <?= nl2br(esc($task['status_remarks'])) ?>
        </div>
    </div>
    <?php endif; ?>

    <?php if (!empty($task['remarks'])): ?>
    <div class="form-group">
        <label class="form-label">Remarks</label>
        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px;">
            <?= nl2br(esc($task['remarks'])) ?>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="form-group">
        <label class="form-label">Assigned Date</label>
        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px;">
            <?= date('M j, Y g:i A', strtotime($task['assigned_at'])) ?>
        </div>
    </div>
</div>

<!-- Task Actions -->
<?php if ($task['status'] == 'active' || $task['status'] == 'planned'): ?>
<div class="card">
    <h2 class="card-title">⚡ Quick Actions</h2>

    <div class="row">
        <div class="col col-6">
            <button class="btn btn-success btn-block" onclick="updateTaskStatus('completed')">
                ✅ Mark as Complete
            </button>
        </div>
        <div class="col col-6">
            <button class="btn btn-secondary btn-block" onclick="showRemarksForm()">
                💬 Add Remarks
            </button>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Status Update Form -->
<div class="card" id="statusUpdateCard" style="display: none;">
    <h2 class="card-title">📝 Update Task Status</h2>
    
    <form method="post" action="<?= base_url('field/tasks/update-status') ?>">
        <input type="hidden" name="task_id" value="<?= $task['activity_id'] ?>">
        
        <div class="form-group">
            <label class="form-label">New Status</label>
            <select name="status" class="form-control" required>
                <option value="">Select Status</option>
                <?php if ($task['status'] == 'active' || $task['status'] == 'planned'): ?>
                    <option value="completed">Mark as Complete</option>
                <?php endif; ?>
            </select>
        </div>
        
        <div class="form-group">
            <label class="form-label">Remarks (Optional)</label>
            <textarea name="remarks" class="form-control" rows="3" placeholder="Add any comments or notes about this task..."></textarea>
        </div>
        
        <div class="row">
            <div class="col col-6">
                <button type="button" class="btn btn-secondary btn-block" onclick="hideStatusForm()">
                    Cancel
                </button>
            </div>
            <div class="col col-6">
                <button type="submit" class="btn btn-success btn-block">
                    Update Status
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Task Progress -->
<div class="card">
    <h2 class="card-title">📈 Task Progress</h2>
    
    <?php
    $daysTotal = (strtotime($task['date_to']) - strtotime($task['date_from'])) / (60 * 60 * 24);
    $daysPassed = (time() - strtotime($task['date_from'])) / (60 * 60 * 24);
    $daysRemaining = (strtotime($task['date_to']) - time()) / (60 * 60 * 24);
    
    $progressPercent = 0;
    if ($daysTotal > 0) {
        $progressPercent = min(100, max(0, ($daysPassed / $daysTotal) * 100));
    }
    ?>
    
    <div style="margin-bottom: 15px;">
        <div style="background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden;">
            <div style="background: <?= $daysRemaining < 0 ? '#dc3545' : ($daysRemaining <= 2 ? '#ffc107' : '#28a745') ?>; height: 100%; width: <?= $progressPercent ?>%; transition: width 0.3s ease;"></div>
        </div>
    </div>
    
    <div class="row">
        <div class="col col-6">
            <div style="text-align: center;">
                <div style="font-size: 18px; font-weight: bold; color: #007bff;">
                    <?= max(0, ceil($daysRemaining)) ?>
                </div>
                <div style="font-size: 12px; color: #666;">Days Remaining</div>
            </div>
        </div>
        <div class="col col-6">
            <div style="text-align: center;">
                <div style="font-size: 18px; font-weight: bold; color: #28a745;">
                    <?= round($progressPercent) ?>%
                </div>
                <div style="font-size: 12px; color: #666;">Time Elapsed</div>
            </div>
        </div>
    </div>
    
    <?php if ($daysRemaining < 0): ?>
        <div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 6px; margin-top: 10px; text-align: center;">
            ⚠️ This task is overdue by <?= abs(ceil($daysRemaining)) ?> day(s)
        </div>
    <?php elseif ($daysRemaining <= 2): ?>
        <div style="background: #fff3cd; color: #856404; padding: 10px; border-radius: 6px; margin-top: 10px; text-align: center;">
            ⏰ This task is due soon (<?= ceil($daysRemaining) ?> day(s) remaining)
        </div>
    <?php endif; ?>
</div>

<script>
function updateTaskStatus(status) {
    var statusText = status === 'completed' ? 'completed' : status;
    if (confirm('Are you sure you want to mark this task as ' + statusText + '?')) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'post';
        form.action = '<?= base_url('field/tasks/update-status') ?>';
        
        const taskIdInput = document.createElement('input');
        taskIdInput.type = 'hidden';
        taskIdInput.name = 'task_id';
        taskIdInput.value = '<?= $task['activity_id'] ?>';
        
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = status;
        
        form.appendChild(taskIdInput);
        form.appendChild(statusInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function showRemarksForm() {
    document.getElementById('statusUpdateCard').style.display = 'block';
    document.querySelector('select[name="status"]').value = 'submitted';
}

function hideStatusForm() {
    document.getElementById('statusUpdateCard').style.display = 'none';
}
</script>

<?= $this->endSection() ?>
