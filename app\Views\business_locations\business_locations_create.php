<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-plus-circle me-2"></i><?= $title ?>
            </h1>
            <p class="text-muted mb-0">Add a new business location to the system</p>
        </div>
        <div>
            <a href="<?= base_url('admin/business-locations') ?>" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to List
            </a>
        </div>
    </div>

    <!-- Error Messages -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Create Form -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-geo-alt me-2"></i>Business Location Information
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post" action="<?= base_url('admin/business-locations/create') ?>">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <!-- Business Entity -->
                            <div class="col-md-6 mb-3">
                                <label for="business_entity_id" class="form-label">Business Entity <span class="text-danger">*</span></label>
                                <select class="form-select" id="business_entity_id" name="business_entity_id" required>
                                    <option value="">Select Business Entity</option>
                                    <?php foreach ($entities as $entity): ?>
                                        <option value="<?= $entity['id'] ?>" 
                                                <?= (old('business_entity_id', $selectedEntity) == $entity['id']) ? 'selected' : '' ?>>
                                            <?= esc($entity['business_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">Select the parent business entity</div>
                            </div>

                            <!-- Business Location/Branch -->
                            <div class="col-md-6 mb-3">
                                <label for="business_name" class="form-label">Business Location/Branch <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="business_name" name="business_name"
                                       value="<?= old('business_name') ?>" required maxlength="150"
                                       placeholder="Enter business location or branch name">
                                <div class="form-text">Name of this specific business location or branch (max 150 characters)</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Location Type -->
                            <div class="col-md-6 mb-3">
                                <label for="type" class="form-label">Location Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">Select Location Type</option>
                                    <option value="retail" <?= old('type') == 'retail' ? 'selected' : '' ?>>Retail</option>
                                    <option value="wholesale" <?= old('type') == 'wholesale' ? 'selected' : '' ?>>Wholesale</option>
                                </select>
                                <div class="form-text">Select whether this is a retail or wholesale location</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Country -->
                            <div class="col-md-4 mb-3">
                                <label for="country_id" class="form-label">Country</label>
                                <select class="form-select" id="country_id" name="country_id">
                                    <option value="">Select Country</option>
                                    <?php foreach ($countries as $id => $name): ?>
                                        <option value="<?= $id ?>" <?= old('country_id') == $id ? 'selected' : '' ?>><?= esc($name) ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">Country where the location is situated</div>
                            </div>

                            <!-- Province -->
                            <div class="col-md-4 mb-3">
                                <label for="province_id" class="form-label">Province/State</label>
                                <select class="form-select" id="province_id" name="province_id" disabled>
                                    <option value="">Select Province</option>
                                </select>
                                <div class="form-text">Province or state within the country</div>
                            </div>

                            <!-- District -->
                            <div class="col-md-4 mb-3">
                                <label for="district_id" class="form-label">District/City</label>
                                <select class="form-select" id="district_id" name="district_id" disabled>
                                    <option value="">Select District</option>
                                </select>
                                <div class="form-text">District or city within the province</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- GPS Coordinates -->
                            <div class="col-md-12 mb-3">
                                <label for="gps_coordinates" class="form-label">GPS Coordinates</label>
                                <input type="text" class="form-control" id="gps_coordinates" name="gps_coordinates"
                                       value="<?= old('gps_coordinates') ?>" maxlength="200"
                                       placeholder="e.g., -6.2088, 106.8456 or 6°12'31.7&quot;S 106°50'44.2&quot;E">
                                <div class="form-text">GPS coordinates in decimal degrees or degrees/minutes/seconds format (optional)</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Remarks -->
                            <div class="col-md-12 mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3"
                                          placeholder="Optional notes or description about this location"><?= old('remarks') ?></textarea>
                                <div class="form-text">Additional information or notes (optional)</div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <hr class="my-4">
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/business-locations') ?>" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-1"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-1"></i>Create Business Location
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Select2 JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const entitySelect = document.getElementById('business_entity_id');
    const locationNameInput = document.getElementById('business_name');


    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Validate business entity
        if (!entitySelect.value) {
            isValid = false;
            entitySelect.classList.add('is-invalid');
        } else {
            entitySelect.classList.remove('is-invalid');
        }
        
        // Validate location name
        if (!locationNameInput.value.trim()) {
            isValid = false;
            locationNameInput.classList.add('is-invalid');
        } else {
            locationNameInput.classList.remove('is-invalid');
        }
        

        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
    
    // Remove validation classes on input
    entitySelect.addEventListener('change', function() {
        this.classList.remove('is-invalid');
    });
    
    locationNameInput.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });


});
</script>

<script>
$(document).ready(function() {
    // Initialize Select2 for all dropdowns
    $('#business_entity_id').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select Business Entity',
        allowClear: true
    });

    $('#country_id').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select Country',
        allowClear: true
    });

    $('#province_id').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select Province',
        allowClear: true
    });

    $('#district_id').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select District',
        allowClear: true
    });

    // Country change event
    $('#country_id').on('change', function() {
        const countryId = $(this).val();
        const provinceSelect = $('#province_id');
        const districtSelect = $('#district_id');

        // Reset province and district
        provinceSelect.empty().append('<option value="">Select Province</option>').prop('disabled', true).trigger('change');
        districtSelect.empty().append('<option value="">Select District</option>').prop('disabled', true).trigger('change');

        if (countryId) {
            // Fetch provinces
            $.ajax({
                url: '<?= base_url('admin/business-locations/get-provinces') ?>',
                type: 'POST',
                data: {
                    country_id: countryId,
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.provinces && Object.keys(response.provinces).length > 0) {
                        $.each(response.provinces, function(id, name) {
                            provinceSelect.append('<option value="' + id + '">' + name + '</option>');
                        });
                        provinceSelect.prop('disabled', false);
                    } else {
                        console.log('No provinces found for country ID:', countryId);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading provinces:', error, xhr.responseText);
                    alert('Error loading provinces. Please try again.');
                }
            });
        }
    });

    // Province change event
    $('#province_id').on('change', function() {
        const provinceId = $(this).val();
        const districtSelect = $('#district_id');

        // Reset district
        districtSelect.empty().append('<option value="">Select District</option>').prop('disabled', true).trigger('change');

        if (provinceId) {
            // Fetch districts
            $.ajax({
                url: '<?= base_url('admin/business-locations/get-districts') ?>',
                type: 'POST',
                data: {
                    province_id: provinceId,
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.districts && Object.keys(response.districts).length > 0) {
                        $.each(response.districts, function(id, name) {
                            districtSelect.append('<option value="' + id + '">' + name + '</option>');
                        });
                        districtSelect.prop('disabled', false);
                    } else {
                        console.log('No districts found for province ID:', provinceId);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading districts:', error, xhr.responseText);
                    alert('Error loading districts. Please try again.');
                }
            });
        }
    });
});
</script>

<?= $this->endSection() ?>
